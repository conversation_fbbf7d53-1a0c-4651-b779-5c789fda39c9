<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title><?php echo e(config('app.name', 'Laravel')); ?></title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <!-- Icons -->
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

        <!-- Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    </head>
    <body class="font-inter antialiased bg-gray-50">
        <div class="min-h-screen flex">
            <!-- Sidebar -->
            <?php echo $__env->make('layouts.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            <!-- Main Content Area -->
            <div class="flex-1 flex flex-col">
                <!-- Topbar -->
                <?php echo $__env->make('layouts.topbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                <!-- Page Content -->
                <main class="flex-1 p-6 bg-gray-50 overflow-y-auto">
                    <!-- Page Heading -->
                    <?php if(isset($header)): ?>
                        <div class="mb-6">
                            <h1 class="text-2xl font-semibold text-gray-900"><?php echo e($header); ?></h1>
                        </div>
                    <?php endif; ?>

                    <!-- Flash Messages -->
                    <?php if(session('success')): ?>
                        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline"><?php echo e(session('success')); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline"><?php echo e(session('error')); ?></span>
                        </div>
                    <?php endif; ?>

                    <!-- Content -->
                    <?php echo e($slot); ?>

                </main>
            </div>
        </div>

        <!-- Custom Styles -->
        <style>
            :root {
                --primary: #007BCE;
                --primary-dark: #005B99;
                --primary-light: #3CB5F9;
                --accent: #00CFFF;
                --sidebar-start: #002D4D;
                --sidebar-end: #007BCE;
                --bg-light: #F5F8FA;
                --text-primary: #1A1A1A;
                --text-secondary: #5A5A5A;
                --success: #28a745;
                --danger: #dc3545;
                --warning: #ffc107;
            }

            .font-inter {
                font-family: 'Inter', sans-serif;
            }

            .bg-primary { background-color: var(--primary); }
            .bg-primary-dark { background-color: var(--primary-dark); }
            .bg-primary-light { background-color: var(--primary-light); }
            .bg-accent { background-color: var(--accent); }
            .bg-sidebar-gradient {
                background: linear-gradient(135deg, var(--sidebar-start) 0%, var(--sidebar-end) 100%);
            }

            .text-primary { color: var(--primary); }
            .text-primary-dark { color: var(--primary-dark); }
            .text-accent { color: var(--accent); }

            .border-primary { border-color: var(--primary); }
            .hover\:bg-accent:hover { background-color: var(--accent); }
            .hover\:text-accent:hover { color: var(--accent); }
        </style>
    </body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\Programmarti Gestionale\programmarti-gest\resources\views/layouts/app.blade.php ENDPATH**/ ?>