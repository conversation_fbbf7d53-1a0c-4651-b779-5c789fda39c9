<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <!-- Icons -->
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-inter antialiased bg-gray-50">
        <div class="min-h-screen flex">
            <!-- Sidebar -->
            @include('layouts.sidebar')

            <!-- Main Content Area -->
            <div class="flex-1 flex flex-col">
                <!-- Topbar -->
                @include('layouts.topbar')

                <!-- Page Content -->
                <main class="flex-1 p-6 bg-gray-50 overflow-y-auto">
                    <!-- Page Heading -->
                    @isset($header)
                        <div class="mb-6">
                            <h1 class="text-2xl font-semibold text-gray-900">{{ $header }}</h1>
                        </div>
                    @endisset

                    <!-- Flash Messages -->
                    @if(session('success'))
                        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ session('error') }}</span>
                        </div>
                    @endif

                    <!-- Content -->
                    {{ $slot }}
                </main>
            </div>
        </div>

        <!-- Custom Styles -->
        <style>
            :root {
                --primary: #007BCE;
                --primary-dark: #005B99;
                --primary-light: #3CB5F9;
                --accent: #00CFFF;
                --sidebar-start: #002D4D;
                --sidebar-end: #007BCE;
                --bg-light: #F5F8FA;
                --text-primary: #1A1A1A;
                --text-secondary: #5A5A5A;
                --success: #28a745;
                --danger: #dc3545;
                --warning: #ffc107;
            }

            .font-inter {
                font-family: 'Inter', sans-serif;
            }

            .bg-primary { background-color: var(--primary); }
            .bg-primary-dark { background-color: var(--primary-dark); }
            .bg-primary-light { background-color: var(--primary-light); }
            .bg-accent { background-color: var(--accent); }
            .bg-sidebar-gradient {
                background: linear-gradient(135deg, var(--sidebar-start) 0%, var(--sidebar-end) 100%);
            }

            .text-primary { color: var(--primary); }
            .text-primary-dark { color: var(--primary-dark); }
            .text-accent { color: var(--accent); }

            .border-primary { border-color: var(--primary); }
            .hover\:bg-accent:hover { background-color: var(--accent); }
            .hover\:text-accent:hover { color: var(--accent); }
        </style>
    </body>
</html>
